<template>
	<view class="page">
		<view class="box">
			<view class="title">账户余额</view>
			<view class="money">￥{{service_price}}</view>
			<view class="btn" :class="{ 'btn-disabled': isButtonDisabled }" @tap="goTx">提现</view>
			<view class="circle"></view>
		</view>
		<view class="name">收支明细</view>
		<view class="record">
			<view class="list_item" v-for="(item,index) in list" :key="index">
				<view class="left">
					<image src="../static/images/8957.png" mode=""></image>
				</view>
				<view class="mid">
					<view class="name1">{{typeArr[item.type]}}</view>
					<view class="time">{{item.createTime}}</view>
				</view>
				<view class="right">{{(item.type == 0 || item.type == 1)?'+':'-'}}{{item.price}}</view>
			</view>
		</view>
		<view class="" style="display: flex;justify-content: center;" v-if="list.length>=10">
			<u-loadmore :status="status" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				typeArr: ['服务收入', '分销佣金', '服务提现', '佣金提现'],
				list: [],
				service_price: 0,
				status: 'loadmore',
				page: 1,
				limit: 10,
				info: '',
				userId: null, // 新增用户ID字段
				isButtonDisabled: false, // 新增控制按钮禁用状态的字段
				form: {
					coach_id: '',
					type: "0,2",
					page: 1
				},
			}
		},
		methods: {
			goTx() {
				if (this.isButtonDisabled) {
					uni.showToast({
						icon: 'none',
						title: '提现功能暂不可用'
					})
					return
				}
				if (this.info.status === 2) {
					uni.navigateTo({
						url: '/shifu/coachCashOut'
					})
				} else {
					uni.showToast({
						icon: 'none',
						title: '请成为师傅'
					})
				}
			},
			async loadData() {
				try {
					const res = await this.$api.shifu.getSInfo()
					const res2 = await this.$api.shifu.incomeSee({
						type: [0, 2],
						pageNum: this.page,
						pageSize: this.limit
					})
					console.log(res2)
					if (res2 === -1) {
						uni.showToast({
							icon: 'none',
							title: '你当前师傅状态在审核中，请耐心等待'
						})
					} else {
						this.service_price = res.data.servicePrice
						this.list = res2.data.list
						if (res2.data.list.length < this.limit) {
							this.status = 'nomore'
						} else {
							this.status = 'loadmore'
						}
					}
					this.info = res.data
					// 假设用户信息中包含userId，这里模拟获取用户ID
					this.userId = res.data.userId || 11431 // 替换为实际获取userId的方式
					this.isButtonDisabled = this.data.userId === 11431 // 判断是否为目标用户
				} catch (error) {
					uni.showToast({
						icon: 'none',
						title: '数据加载失败，请稍后重试'
					})
				}
			}
		},
		async onLoad() {
			this.page = 1
			this.list = []
			await this.loadData()
		},
		onReachBottom() {
			if (this.status === 'nomore') return
			this.status = 'loading'
			setTimeout(async () => {
				try {
					this.page++
					const res = await this.$api.shifu.incomeSee({
						type: [0, 2],
						pageNum: this.page,
						pageSize: this.limit
					})
					if (res.data.list.length === 0 || res.data.list.length < this.limit) {
						this.status = 'nomore'
					} else {
						this.status = 'loadmore'
					}
					this.list = [...this.list, ...res.data.list]
				} catch (error) {
					this.status = 'loadmore'
					this.page--
					uni.showToast({
						icon: 'none',
						title: '加载更多失败，请稍后重试'
					})
				}
			}, 1000)
		},
	}
</script>

<style scoped lang="scss">
	.page {
		height: 100vh;
		background-color: #fff;
		padding: 40rpx 30rpx;

		.box {
			width: 690rpx;
			height: 316rpx;
			position: relative;
			background: #0D88F9;
			border-radius: 24rpx 24rpx 24rpx 24rpx;
			color: #fff;
			padding: 98rpx 40rpx;
			overflow: hidden;

			.title {
				font-size: 24rpx;
				font-weight: 500;
			}

			.money {
				font-size: 48rpx;
				font-weight: 500;
				margin-top: 20rpx;
			}

			.btn {
				width: 132rpx;
				height: 66rpx;
				background: #FABC3F;
				border-radius: 34rpx 34rpx 34rpx 34rpx;
				line-height: 66rpx;
				text-align: center;
				font-size: 24rpx;
				font-weight: 500;
				position: absolute;
				right: 38rpx;
				top: 72rpx;
				z-index: 2;
			}

			.btn-disabled {
				background: #cccccc !important;
				color: #666666 !important;
				pointer-events: none; // 禁用点击事件
			}

			.circle {
				width: 350rpx;
				height: 350rpx;
				border-radius: 50%;
				background-color: #fff;
				color: #fff;
				opacity: 0.2;
				position: absolute;
				right: -100rpx;
				bottom: -120rpx;
				z-index: 1;
			}
		}

		.name {
			margin: 40rpx 0;
			font-size: 32rpx;
			font-weight: 500;
			color: #171717;
		}

		.record {
			width: 100%;

			.list_item {
				height: 102rpx;
				display: flex;

				.left {
					width: 78rpx;
					height: 78rpx;
					border-radius: 50%;
					background: #F9F9F9;
					display: flex;
					justify-content: center;
					align-items: center;

					image {
						width: 33rpx;
						height: 31rpx;
					}
				}

				.mid {
					margin-left: 20rpx;
					width: 520rpx;

					.name1 {
						font-size: 28rpx;
						font-weight: 500;
						color: #171717;
						max-width: 500rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
					}

					.time {
						margin-top: 12rpx;
						font-size: 24rpx;
						font-weight: 400;
						color: #999999;
					}
				}

				.right {
					width: 92rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 28rpx;
					font-weight: 500;
					color: #171717;
				}
			}
		}
	}
</style>