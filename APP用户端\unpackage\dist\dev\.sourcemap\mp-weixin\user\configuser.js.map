{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/configuser.vue?abc6", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/configuser.vue?2e15", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/configuser.vue?164b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/configuser.vue?271c", "uni-app:///user/configuser.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/configuser.vue?2440", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/configuser.vue?e0d5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "info", "methods", "onLoad", "console", "options", "ress", "res", "uni", "title", "icon", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCS72B;EACAC;IACA;MACAC;IACA;EACA;EACAC,UAEA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cAAA;cAAA,MAGAC;gBAAA;gBAAA;cAAA;cACAD;cAAA;cAAA,OACA;YAAA;cAAAE;cACAF;cACAG,iBACA;cACA;gBACA;gBACAH;cACA;gBACA;gBACAA;cACA;gBACA;gBACAA;cACA;gBACAA;gBACA;cACA;cAAA;cAAA;YAAA;cAEAA;cAAA;cAAA,OACA;YAAA;cAAAE;cACAF;cACAG,mBACA;cACA;gBACA;gBACAH;cACA;gBACA;gBACAA;cACA;gBACA;gBACAA;cACA;gBACAA;gBACA;cACA;YAAA;cAGAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAA;cACA;;cAEA;cACAI;gBACAC;gBACAC;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/configuser.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/configuser.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./configuser.vue?vue&type=template&id=1bea9d33&scoped=true&\"\nvar renderjs\nimport script from \"./configuser.vue?vue&type=script&lang=js&\"\nexport * from \"./configuser.vue?vue&type=script&lang=js&\"\nimport style0 from \"./configuser.vue?vue&type=style&index=0&id=1bea9d33&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1bea9d33\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/configuser.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./configuser.vue?vue&type=template&id=1bea9d33&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./configuser.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./configuser.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"agreement-container\">\n\t\t<view class=\"content-wrapper\">\n\t\t\t<rich-text class=\"agreement-content\" :nodes=\"info\"></rich-text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tinfo:''\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t\n\t\t},\n\t\tasync onLoad(options) {\n\t\t\tconsole.log('=== 协议页面加载 ===', options)\n\n\t\t\ttry {\n\t\t\t\tif(options.type === \"privacy\"){\n\t\t\t\t\tconsole.log('=== 加载隐私政策 ===')\n\t\t\t\t\tconst ress = await this.$api.base.getConfig()\n\t\t\t\t\tconsole.log('隐私政策API响应:', res)\n\t\t\t\t\tlet res=ress.data\n\t\t\t\t\t// 检查多种可能的字段名\n\t\t\t\t\tif (res.content) {\n\t\t\t\t\t\tthis.info = res.data.content\n\t\t\t\t\t\tconsole.log('使用content字段，内容长度:', res.data.content.length)\n\t\t\t\t\t} else if (res.privacyPolicy) {\n\t\t\t\t\t\tthis.info = res.data.privacyPolicy\n\t\t\t\t\t\tconsole.log('使用privacyPolicy字段，内容长度:', res.data.privacyPolicy.length)\n\t\t\t\t\t} else if (res.privacy) {\n\t\t\t\t\t\tthis.info = res.data.privacy\n\t\t\t\t\t\tconsole.log('使用privacy字段，内容长度:', res.data.privacy.length)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('未找到隐私政策内容，可用字段:', Object.keys(res))\n\t\t\t\t\t\tthis.info = '<p>隐私政策内容加载失败，请稍后重试</p>'\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('=== 加载服务协议 ===')\n\t\t\t\t\tconst ress = await this.$api.base.getConfig()\n\t\t\t\t\tconsole.log('服务协议API响应:', res)\n\t\t\t\t\tlet res=ress.data\n\t\t\t\t\t// 检查多种可能的字段名\n\t\t\t\t\tif (res.loginProtocol) {\n\t\t\t\t\t\tthis.info = res.loginProtocol\n\t\t\t\t\t\tconsole.log('使用loginProtocol字段，内容长度:', res.loginProtocol.length)\n\t\t\t\t\t} else if (res.serviceAgreement) {\n\t\t\t\t\t\tthis.info = res.serviceAgreement\n\t\t\t\t\t\tconsole.log('使用serviceAgreement字段，内容长度:', res.serviceAgreement.length)\n\t\t\t\t\t} else if (res.protocol) {\n\t\t\t\t\t\tthis.info = res.protocol\n\t\t\t\t\t\tconsole.log('使用protocol字段，内容长度:', res.protocol.length)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('未找到服务协议内容，可用字段:', Object.keys(res))\n\t\t\t\t\t\tthis.info = '<p>服务协议内容加载失败，请稍后重试</p>'\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tconsole.log('=== 协议内容加载完成 ===')\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载协议内容失败:', error)\n\t\t\t\tthis.info = '<p>内容加载失败，请检查网络连接后重试</p>'\n\n\t\t\t\t// 显示错误提示\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载失败，请重试',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n.agreement-container {\n\tmin-height: 100vh;\n\tbackground-color: #f8f9fa;\n\tpadding: 20rpx;\n}\n\n.content-wrapper {\n\tbackground-color: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 40rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.agreement-content {\n\tline-height: 1.8;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\n\t/* 处理富文本内容样式 */\n\t:deep(p) {\n\t\tmargin-bottom: 20rpx;\n\t\ttext-indent: 2em;\n\t}\n\n\t:deep(h1), :deep(h2), :deep(h3) {\n\t\tfont-weight: bold;\n\t\tmargin: 30rpx 0 20rpx;\n\t\tcolor: #222;\n\t}\n\n\t:deep(h1) {\n\t\tfont-size: 36rpx;\n\t}\n\n\t:deep(h2) {\n\t\tfont-size: 32rpx;\n\t}\n\n\t:deep(h3) {\n\t\tfont-size: 30rpx;\n\t}\n\n\t:deep(ul), :deep(ol) {\n\t\tpadding-left: 40rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t:deep(li) {\n\t\tmargin-bottom: 10rpx;\n\t}\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./configuser.vue?vue&type=style&index=0&id=1bea9d33&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./configuser.vue?vue&type=style&index=0&id=1bea9d33&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754814577411\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}