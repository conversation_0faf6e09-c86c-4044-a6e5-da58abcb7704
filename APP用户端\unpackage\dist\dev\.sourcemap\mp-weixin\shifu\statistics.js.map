{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/statistics.vue?3382", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/statistics.vue?54e8", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/statistics.vue?7f47", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/statistics.vue?ad21", "uni-app:///shifu/statistics.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/statistics.vue?2119", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/statistics.vue?02f0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "page", "userInfo", "list", "total", "limit", "loading", "onPullDownRefresh", "console", "setTimeout", "uni", "onReachBottom", "methods", "getUserInfo", "getList", "getMyTeam", "pageNum", "pageSize", "then", "finally", "loadMore", "mounted"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0C72B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACA;IACA;IACA;IACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA,kBACAC;QACAC;QACAC;MACA,GACAC;QACA;QACA;QACA;MACA,GACAC;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/statistics.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/statistics.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./statistics.vue?vue&type=template&id=921da386&scoped=true&\"\nvar renderjs\nimport script from \"./statistics.vue?vue&type=script&lang=js&\"\nexport * from \"./statistics.vue?vue&type=script&lang=js&\"\nimport style0 from \"./statistics.vue?vue&type=style&index=0&id=921da386&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"921da386\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/statistics.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./statistics.vue?vue&type=template&id=921da386&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./statistics.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./statistics.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page\">\n    <view class=\"header\">\n      <view class=\"head\">\n        <view class=\"left\">\n          <image :src=\"userInfo.avatarUrl ? userInfo.avatarUrl : '/static/mine/default_user.png'\" mode=\"\"></image>\n          <view class=\"name\">{{ userInfo.nickName || '' }}</view>\n        </view>\n        <view class=\"right\">已邀请{{ total }}</view>\n      </view>\n    </view>\n    <view class=\"fg\"></view>\n    <view class=\"box\">\n      <view class=\"title\">我邀请的</view>\n      <view class=\"list\">\n        <view style=\"display: flex; justify-content: space-between; align-items: center;\" class=\"list_item\" v-for=\"(item, index) in list\" :key=\"index\">\n        <view style=\"display: flex; justify-content: space-between; align-items: center;\" class=\"\">\r\n        \t<image :src=\"item.avatarUrl ? item.avatarUrl : '/static/mine/default_user.png'\" mode=\"\"></image>\r\n        \t<view class=\"info\">\r\n        \t  <view class=\"nam\">{{ item.nickName }}</view>\r\n        \t  <view class=\"nam\">{{ item.phone }}</view>\r\n        \t</view>\r\n        </view>\r\n\t\t<view>\r\n\t\t\t<view  class=\"\">\r\n\t\t\t  <view style=\"display: flex; justify-content: center; align-items: center;\" class=\"\">\r\n\t\t\t  \t  {{item.shifu===0?\"用户\":\"师傅\"}}\r\n\t\t\t  </view>\r\n\t\t\t  <view style=\"font-size: 24rpx;\" class=\"\">\r\n\t\t\t  \t  {{item.createTime}}\r\n\t\t\t  </view>\r\n\t\t\t</view>\r\n\t\t</view>\n        </view>\n      </view>\n      <!-- Optional: Load More Button -->\n      <view v-if=\"list.length < total\" class=\"load-more\" @click=\"loadMore\">加载更多</view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      page: 1,\n      userInfo: {},\n      list: [],\n      total: 0, // Initialize as 0\n      limit: 10,\n      loading: false // Prevent multiple simultaneous requests\n    };\n  },\n  onPullDownRefresh() {\n    console.log('refresh');\n    // Reset to first page on pull-down refresh\n    this.page = 1;\n    this.list = [];\n    this.getList();\n    setTimeout(() => {\n      uni.stopPullDownRefresh();\n    }, 1000);\n  },\n  onReachBottom() {\n    // Triggered when user scrolls to the bottom\n    if (this.list.length < this.total && !this.loading) {\n      this.page += 1;\n      this.getList();\n    }\n  },\n  methods: {\n    getUserInfo() {\n      this.$api.user.userInfo().then(res => {\n        this.userInfo = res;\n      });\n    },\n    getList() {\n      if (this.loading) return; // Prevent multiple requests\n      this.loading = true;\n      this.$api.service\n        .getMyTeam({\n          pageNum: this.page,\n          pageSize: this.limit\n        })\n        .then(res => {\n          this.total = res.totalCount;\n          // Append new items to the list\n          this.list = this.page === 1 ? res.list : [...this.list, ...res.list];\n        })\n        .finally(() => {\n          this.loading = false;\n        });\n    },\n    loadMore() {\n      // Optional: Manual load more button\n      if (this.list.length < this.total && !this.loading) {\n        this.page += 1;\n        this.getList();\n      }\n    }\n  },\n  mounted() {\n    this.getUserInfo();\n    this.getList();\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n  .header {\n    padding: 40rpx 30rpx;\n\n    .head {\n      width: 690rpx;\n      height: 186rpx;\n      background: #2e80fe;\n      border-radius: 20rpx;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 0 30rpx;\n\n      .left {\n        display: flex;\n        align-items: center;\n\n        image {\n          width: 106rpx;\n          height: 106rpx;\n          border-radius: 50%;\n        }\n\n        .name {\n          margin-left: 20rpx;\n          font-size: 32rpx;\n          font-weight: 500;\n          color: #ffffff;\n          max-width: 240rpx;\n          overflow: hidden;\n          white-space: nowrap;\n          text-overflow: ellipsis;\n        }\n      }\n\n      .right {\n        width: fit-content;\n        height: 74rpx;\n        background: #81b3ff;\n        border-radius: 12rpx;\n        line-height: 74rpx;\n        text-align: center;\n        padding: 0 14rpx;\n        font-size: 32rpx;\n        font-weight: 500;\n        color: #ffffff;\n      }\n    }\n  }\n\n  .fg {\n    background: #f3f4f5;\n    width: 100%;\n    height: 20rpx;\n  }\n\n  .box {\n    padding: 40rpx 30rpx;\n\n    .title {\n      font-size: 32rpx;\n      font-weight: 500;\n      color: #171717;\n    }\n\n    .list {\n      margin-top: 42rpx;\n\n      .list_item {\n        display: flex;\n        margin-bottom: 20rpx;\n\n        image {\n          width: 104rpx;\n          height: 104rpx;\n          border-radius: 50%;\n        }\n\n        .info {\n          margin-left: 20rpx;\n\n          .nam {\n            font-size: 28rpx;\n            font-weight: 400;\n            color: #171717;\n            max-width: 480rpx;\n            overflow: hidden;\n            white-space: nowrap;\n            text-overflow: ellipsis;\n          }\n\n          .phone {\n            margin-top: 20rpx;\n            font-size: 28rpx;\n            font-weight: 400;\n            color: #999999;\n          }\n        }\n      }\n    }\n\n    .load-more {\n      text-align: center;\n      padding: 20rpx;\n      font-size: 28rpx;\n      color: #2e80fe;\n      cursor: pointer;\n    }\n  }\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./statistics.vue?vue&type=style&index=0&id=921da386&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./statistics.vue?vue&type=style&index=0&id=921da386&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754814570148\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}