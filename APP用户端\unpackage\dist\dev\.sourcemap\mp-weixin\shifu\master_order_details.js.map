{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_order_details.vue?8615", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_order_details.vue?3263", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_order_details.vue?3cb7", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_order_details.vue?2ac2", "uni-app:///shifu/master_order_details.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_order_details.vue?16fd", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/master_order_details.vue?400c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Upload", "data", "ready", "id", "configInfo", "selectedItem", "payType", "Info", "imageErrors", "fallbackImage", "showImageModal", "currentImage", "show", "getconfigs", "confirmshow", "masterModalShow", "showNameIdModal", "content", "input", "orderData", "tmplIds", "tempForm", "<PERSON><PERSON><PERSON>", "idCode", "id_card1", "id_card2", "computed", "orderDetails", "label", "value", "alignRight", "shouldShowQuoteButton", "methods", "validateInput", "parts", "handleQuote", "textsss", "uni", "success", "console", "fail", "close", "confirmBao", "icon", "title", "updatedOrderData", "orderId", "price", "res", "setTimeout", "url", "imgUploadTemp", "imgtype", "saveNameIdInfo", "p", "shi<PERSON><PERSON>", "userId", "payload", "idCard", "goToSettle", "formatMobile", "formatAddress", "formatHouseNumber", "formatOrderCode", "isValidImageUrl", "splitImageUrls", "godh", "latitude", "longitude", "scale", "name", "address", "getDetail", "orderCode", "goodsName", "createTime", "mobile", "addressInfo", "houseNumber", "lat", "lng", "type", "onImageError", "urls", "previewImage", "current", "closeImageModal", "onLoad", "onUnload"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,6BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACsC;;;AAGzG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAAm2B,CAAgB,m3BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;ACiIv3B;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC,4DACA,GACA;MACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;IACA;IACA;IACAC;MACA;IACA;EACA;;EACAC;IACA;IACAC;MACA;;MAEA;MACAJ;;MAEA;MACAA;;MAEA;MACA;MACA;QACA;QACAA;MACA;MAEA;QACA;QACA;UACAK;UACAL;QACA;MACA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;IACA;IAEA;IACAM;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;UACAjB;UACAkB;YACAC;UACA;UACAC;YACAD;UACA;QACA;MACA;IACA;IAEAE;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAL;kBACAM;kBACAC;gBACA;gBAAA;cAAA;gBAGAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAT;gBACA;kBACA;gBACA;gBAAA,MACAS;kBAAA;kBAAA;gBAAA;gBACAX;kBACAM;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAGAI;kBAAA;kBAAA;gBAAA;gBACAX;kBACAM;kBACAC;gBACA;gBACA;gBAAA;cAAA;gBAAA,MAGAI;kBAAA;kBAAA;gBAAA;gBACAX;kBACAM;kBACAC;gBACA;gBAAA;cAAA;gBAGAP;kBACAM;kBACAC;gBACA;gBACA;gBACAK;kBACAZ;oBACAa;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAb;kBACAM;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MACAZ;MACA;QAAAa;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,kBACA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAhB;kBACAM;kBACAC;gBACA;gBAAA;cAAA;gBAIAU;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAjB;kBACAM;kBACAC;gBACA;gBAAA;cAAA;gBAGAW;gBACAC;gBACAjB;gBACAA;gBACA;gBACAkB;kBACAnC;kBACAC;kBACApB;kBACAqD;kBACAE;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAV;gBAAA;gBACAT;gBACA;kBAAA;kBACAF;oBACAM;oBACAC;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAP;oBACAM;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;kBACAM;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAe;MACA;MACAtB;QACAa;MACA;IACA;IAEA;IACAU;MACA;MACA;MAEA;IACA;IACA;IACAC;MACA;MACA;MAEA;IACA;IACA;IACAC;MACA;MACA;MAEA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QAAA;MAAA;MACA;MACA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;QAAA;MAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA7B;QACA8B;QACAC;QACAC;QACAC;QACAC;QACAjC;UAAA;QAAA;QACAE;UAAA;QAAA;MACA;IACA;IACA;IACAgC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAxB;gBACAT;gBACAA;gBACA;kBACAF;oBACAO;oBACAD;kBACA;gBACA;gBACA;kBACAN;oBACAO;oBACAD;kBACA;gBACA;gBACA;gBACA;kBACA8B;kBACAC;kBACAC;kBACAC;kBACAC;kBACAN;kBACAO;kBACAC;kBACAC;kBACAC;kBAAA;kBACA9E;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAoC;gBACAF;kBACAO;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAuC;MACA3C;MACA;MACA;MACA4C;MACA;IACA;IACA;IACAC;MACA/C;QACA8C;UAAA;QAAA;QACAE;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;gBACA;gBACA;gBACAhD;cACA;cACA;cACA;cACA;cACA;cACAA;cACAA;cACAA;cAAA,IACA;gBAAA;gBAAA;cAAA;cACAA;cACAF;gBACAO;gBACAD;cACA;cAAA;YAAA;cAAA;cAAA,OAGA;YAAA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACA6C;IACAnD;IACAE;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9hBA;AAAA;AAAA;AAAA;AAA0mD,CAAgB,8jDAAG,EAAC,C;;;;;;;;;;;ACA9nD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/master_order_details.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/master_order_details.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./master_order_details.vue?vue&type=template&id=53648d4e&scoped=true&\"\nvar renderjs\nimport script from \"./master_order_details.vue?vue&type=script&lang=js&\"\nexport * from \"./master_order_details.vue?vue&type=script&lang=js&\"\nimport style0 from \"./master_order_details.vue?vue&type=style&index=0&id=53648d4e&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"53648d4e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/master_order_details.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_order_details.vue?vue&type=template&id=53648d4e&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.ready\n    ? _vm.__map(_vm.Info.settingOrderList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.isValidImageUrl(item.val)\n        var l0 = m0\n          ? _vm.__map(_vm.splitImageUrls(item.val), function (url, imgIndex) {\n              var $orig = _vm.__get_orig(url)\n              var g0 = url.trim()\n              return {\n                $orig: $orig,\n                g0: g0,\n              }\n            })\n          : null\n        var g1 = !m0 ? item.val && item.val.trim() !== \"\" : null\n        return {\n          $orig: $orig,\n          m0: m0,\n          l0: l0,\n          g1: g1,\n        }\n      })\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, url, index, imgIndex) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        url = _temp2.url,\n        index = _temp2.index,\n        imgIndex = _temp2.imgIndex\n      var _temp, _temp2\n      return _vm.onImageError(url, index, imgIndex)\n    }\n    _vm.e1 = function ($event, url) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        url = _temp4.url\n      var _temp3, _temp4\n      return _vm.previewImage(url)\n    }\n    _vm.e2 = function ($event) {\n      _vm.confirmshow = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.masterModalShow = false\n    }\n    _vm.e4 = function ($event) {\n      _vm.showNameIdModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_order_details.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_order_details.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\" v-if=\"ready\">\n\t\t<view class=\"box\">\n\t\t\t<view class=\"title\">\n\t\t\t\t<image src=\"../static/images/8957.png\" mode=\"aspectFill\" class=\"title-icon\"></image>\n\t\t\t\t<span>订单信息</span>\n\t\t\t</view>\n\n\t\t\t<view class=\"info-box\">\n\t\t\t\t<view class=\"info-item\" v-for=\"(info, index) in orderDetails\" :key=\"index\">\n\t\t\t\t\t<text class=\"label\">{{ info.label }}</text>\n\t\t\t\t\t<text :class=\"['value', info.alignRight ? 'align-right' : '']\">{{ info.value }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"navigation-btn\" @click=\"godh\">\n\t\t\t\t\t<image src=\"../static/images/9349.png\" mode=\"aspectFill\" class=\"nav-icon\"></image>\n\t\t\t\t\t<text>导航</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"dynamic-section\" v-for=\"(item, index) in Info.settingOrderList\" :key=\"index\">\n\t\t\t\t<view class=\"title\">{{ item.problemDesc }}</view>\n\t\t\t\t<view class=\"img-box\" v-if=\"isValidImageUrl(item.val)\">\n\t\t\t\t\t<image v-for=\"(url, imgIndex) in splitImageUrls(item.val)\" :key=\"imgIndex\" :src=\"url.trim()\"\n\t\t\t\t\t\tmode=\"aspectFill\" @error=\"onImageError(url, index, imgIndex)\" @click=\"previewImage(url)\"\n\t\t\t\t\t\tclass=\"dynamic-image\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view v-else-if=\"item.val && item.val.trim() !== ''\" class=\"text-box\">\n\t\t\t\t\t<text>{{ item.val }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view v-else class=\"text-box\">\n\t\t\t\t\t<text>无</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"\">\n\t\t\t\t<rich-text :nodes=\"getconfigs.masterNotice\"></rich-text>\n\t\t\t</view>\n\t\t\t<view class=\"image-modal\" v-if=\"showImageModal\" @click=\"closeImageModal\">\n\t\t\t\t<view class=\"modal-content\" @click.stop>\n\t\t\t\t\t<image :src=\"currentImage\" mode=\"aspectFit\" class=\"modal-image\"></image>\n\t\t\t\t\t<view class=\"close-btn\" @click=\"closeImageModal\">关闭</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"bottom-quote-section\">\n\t\t\t<view class=\"quote-btn\" @click=\"handleQuote\">\n\t\t\t\t立即报价\n\t\t\t</view>\n\t\t</view>\n\n\t\t<u-popup :show=\"show\" :round=\"10\" closeable @close=\"close\">\n\t\t\t<view class=\"quote-popup-box\">\n\t\t\t\t<view class=\"popup-title\">立即报价</view>\n\t\t\t\t<view class=\"popup-subtitle\">报价金额</view>\n\t\t\t\t<view class=\"money-input\">\n\t\t\t\t\t<u--input placeholder=\"请输入报价金额\" prefixIcon=\"rmb\" prefixIconStyle=\"font-size: 22px;color: #909399\"\n\t\t\t\t\t\ttype=\"digit\" v-model=\"input\" @input=\"validateInput\" maxlength=\"5\"></u--input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"\">\n\t\t\t\t\t<rich-text :nodes=\"getconfigs.masterNotice\"></rich-text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"confirm-quote-btn\" @click=\"confirmBao\">确认报价</view>\n\t\t\t</view>\n\t\t</u-popup>\n\n\t\t<u-modal :show=\"confirmshow\" :content=\"content\" showCancelButton @confirm=\"confirmRe\"\n\t\t\t@cancel=\"confirmshow = false\"></u-modal>\n\t\t<u-modal :show=\"masterModalShow\" content=\"成为师傅才能操作\" showCancelButton @confirm=\"goToSettle\"\n\t\t\t@cancel=\"masterModalShow = false\"></u-modal>\n\n\t\t<u-modal :show=\"showNameIdModal\" title=\"请完善实名信息以确保提现安全\" confirmText=\"保存\" showCancelButton @confirm=\"saveNameIdInfo\"\n\t\t\t@cancel=\"showNameIdModal = false\" :contentStyle=\"{ padding: '40rpx', background: '#ffffff', borderRadius: '16rpx' }\">\n\t\t\t<view class=\"slot-content\">\n\t\t\t\t<view class=\"main_item\">\n\t\t\t\t\t<view class=\"title\"><span style=\"color: #E41F19;\">*</span>姓名</view>\n\t\t\t\t\t<input type=\"text\" v-model=\"tempForm.coachName\" placeholder=\"请输入姓名\" class=\"modal-input\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"main_item\">\n\t\t\t\t\t<view class=\"title\"><span style=\"color: #E41F19;\">*</span>身份证号</view>\n\t\t\t\t\t<input type=\"text\" v-model=\"tempForm.idCode\" placeholder=\"请输入身份证号\" class=\"modal-input\" />\n\t\t\t\t</view>\n\t\t\t\t<view class=\"main_item\">\n\t\t\t\t\t<view class=\"title\"><span style=\"color: #E41F19;\">*</span>上传身份证照片</view>\n\t\t\t\t\t<view class=\"card\">\n\t\t\t\t\t\t<view class=\"card_item\">\n\t\t\t\t\t\t\t<view class=\"top\">\n\t\t\t\t\t\t\t\t<view class=\"das\">\n\t\t\t\t\t\t\t\t\t<view class=\"up\">\n\t\t\t\t\t\t\t\t\t\t<upload\n\t\t\t\t\t\t\t\t\t\t\t@upload=\"imgUploadTemp\"\n\t\t\t\t\t\t\t\t\t\t\t@del=\"imgUploadTemp\"\n\t\t\t\t\t\t\t\t\t\t\t:imagelist=\"tempForm.id_card1\"\n\t\t\t\t\t\t\t\t\t\t\timgtype=\"id_card1\"\n\t\t\t\t\t\t\t\t\t\t\timgclass=\"id_card_box\"\n\t\t\t\t\t\t\t\t\t\t\ttext=\"身份证人像面\"\n\t\t\t\t\t\t\t\t\t\t\t:imgsize=\"1\"\n\t\t\t\t\t\t\t\t\t\t></upload>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"bottom\">拍摄人像面</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"card_item\">\n\t\t\t\t\t\t\t<view class=\"top\">\n\t\t\t\t\t\t\t\t<view class=\"das\">\n\t\t\t\t\t\t\t\t\t<view class=\"up\">\n\t\t\t\t\t\t\t\t\t\t<upload\n\t\t\t\t\t\t\t\t\t\t\t@upload=\"imgUploadTemp\"\n\t\t\t\t\t\t\t\t\t\t\t@del=\"imgUploadTemp\"\n\t\t\t\t\t\t\t\t\t\t\t:imagelist=\"tempForm.id_card2\"\n\t\t\t\t\t\t\t\t\t\t\timgtype=\"id_card2\"\n\t\t\t\t\t\t\t\t\t\t\timgclass=\"id_card_box\"\n\t\t\t\t\t\t\t\t\t\t\ttext=\"身份证国徽面\"\n\t\t\t\t\t\t\t\t\t\t\t:imgsize=\"1\"\n\t\t\t\t\t\t\t\t\t\t></upload>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"bottom\">拍摄国徽面</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-modal>\n\t</view>\n</template>\n\n<script>\n\timport Upload from '@/components/upload.vue'; // Adjust path to your upload.vue\n\texport default {\n\t\tcomponents: {\n\t\t\tUpload,\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tready: false,\n\t\t\t\tid: '',\n\t\t\t\tconfigInfo: '',\n\t\t\t\tselectedItem: {},\n\t\t\t\tpayType: {},\n\t\t\t\tInfo: {},\n\t\t\t\timageErrors: [], // Track images that failed to load\n\t\t\t\tfallbackImage: '../static/images/placeholder.png', // Placeholder image path\n\t\t\t\tshowImageModal: false, // Control image modal visibility\n\t\t\t\tcurrentImage: '', // Store the currently displayed image URL\n\t\t\t\t// 报价相关数据\n\t\t\t\tshow: false,\n\t\t\t\tgetconfigs:'',\n\t\t\t\tconfirmshow: false,\n\t\t\t\tmasterModalShow: false,\n\t\t\t\tshowNameIdModal: false, // New state for the name/ID modal\n\t\t\t\tcontent: '确认接下该订单吗',\n\t\t\t\tinput: '',\n\t\t\t\torderData: '',\n\t\t\t\ttmplIds: ['', 'DxiqXzK4yxCTYAqmeK9lEs0A5-XCF9Fy7kSyX2vmnk',\n\t\t\t\t\t''\n\t\t\t\t],\n\t\t\t\ttempForm: { // Temporary form for name/ID input\n\t\t\t\t\tcoachName: '',\n\t\t\t\t\tidCode: '',\n\t\t\t\t\tid_card1: [],\n\t\t\t\t\tid_card2: [],\n\t\t\t\t}\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\torderDetails() {\n\t\t\t\treturn [{\n\t\t\t\t\t\tlabel: '订单单号',\n\t\t\t\t\t\tvalue: this.formatOrderCode(this.selectedItem.orderCode),\n\t\t\t\t\t\talignRight: true\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '服务内容',\n\t\t\t\t\t\tvalue: this.selectedItem.goodsName || ''\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '下单时间',\n\t\t\t\t\t\tvalue: this.$util.timestampToTime(this.selectedItem.createTime * 1000) || ''\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '联系方式',\n\t\t\t\t\t\tvalue: this.formatMobile(this.selectedItem.mobile)\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '服务定位',\n\t\t\t\t\t\tvalue: this.formatAddress(this.selectedItem.addressInfo),\n\t\t\t\t\t\talignRight: true\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '服务地址',\n\t\t\t\t\t\tvalue: this.formatAddress(this.selectedItem.address),\n\t\t\t\t\t\talignRight: true\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '门牌号',\n\t\t\t\t\t\tvalue: this.formatHouseNumber(this.selectedItem.houseNumber),\n\t\t\t\t\t\talignRight: true\n\t\t\t\t\t}\n\t\t\t\t];\n\t\t\t},\n\t\t\t// 判断是否显示报价按钮（根据订单类型）\n\t\t\tshouldShowQuoteButton() {\n\t\t\t\treturn this.selectedItem.type === 1; // type为1时显示报价按钮\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// Input validation method\n\t\t\tvalidateInput(e) {\n\t\t\t\tlet value = e.detail ? e.detail.value : e;\n\n\t\t\t\t// Remove all Chinese characters\n\t\t\t\tvalue = value.replace(/[\\u4e00-\\u9fa5]/g, '');\n\n\t\t\t\t// Only allow numbers and decimal point\n\t\t\t\tvalue = value.replace(/[^\\d.]/g, '');\n\n\t\t\t\t// Handle decimal point logic\n\t\t\t\tconst parts = value.split('.');\n\t\t\t\tif (parts.length > 2) {\n\t\t\t\t\t// If there are multiple decimal points, keep only the first\n\t\t\t\t\tvalue = parts[0] + '.' + parts.slice(1).join('');\n\t\t\t\t}\n\n\t\t\t\tif (parts.length === 2) {\n\t\t\t\t\t// If there is a decimal part, limit to two digits after the decimal point\n\t\t\t\t\tif (parts[1].length > 2) {\n\t\t\t\t\t\tparts[1] = parts[1].substring(0, 2);\n\t\t\t\t\t\tvalue = parts[0] + '.' + parts[1];\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Prevent multiple zeros at the beginning (except for 0. scenarios)\n\t\t\t\tif (value.length > 1 && value.charAt(0) === '0' && value.charAt(1) !== '.') {\n\t\t\t\t\tvalue = value.substring(1);\n\t\t\t\t}\n\n\t\t\t\t// Update input value\n\t\t\t\tthis.input = value;\n\t\t\t},\n\n\t\t\t// Handle quote button click\n\t\t\thandleQuote() {\n\t\t\t\tthis.textsss();\n\t\t\t\tthis.orderData = this.selectedItem;\n\t\t\t\tthis.show = true;\n\t\t\t},\n\n\t\t\t// Subscribe message handling\n\t\t\ttextsss() {\n\t\t\t\tconst infodata = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};\n\t\t\t\tif (infodata.status === 2) {\n\t\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\t\ttmplIds: this.tmplIds,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log('requestSubscribeMessage result:', res);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.log('requestSubscribeMessage failed:', err);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tclose() {\n\t\t\t\tthis.show = false;\n\t\t\t\tthis.input = '';\n\t\t\t},\n\n\t\t\t// Confirm quote\n\t\t\tasync confirmBao() {\n\t\t\t\tif (this.input == '' || this.input == 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请输入报价（不能为0哦）'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst updatedOrderData = {\n\t\t\t\t\torderId: this.id,\n\t\t\t\t\tprice: this.input\n\t\t\t\t};\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.updateBao(updatedOrderData);\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.data === -2) {\n\t\t\t\t\t\tthis.masterModalShow = true;\n\t\t\t\t\t}\n\t\t\t\t\tif(res.data === -1){\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t\tif (res.data === -5) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.showNameIdModal = true; // Show the new modal\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\ttitle: '报价成功'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.close();\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: '/shifu/master_bao_list?id=1'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'fail',\n\t\t\t\t\t\ttitle: error.message || '报价失败'\n\t\t\t\t\t});\n\t\t\t\t\tthis.close();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// New method for handling image uploads in the tempForm\n\t\t\timgUploadTemp(e) {\n\t\t\t\tconsole.log('imgUploadTemp event:', e);\n\t\t\t\tconst { imagelist, imgtype } = e;\n\t\t\t\tthis.$set(this.tempForm, imgtype, imagelist);\n\t\t\t},\n\n\t\t\t// New method to save name and ID info\n\t\t\tasync saveNameIdInfo() {\n\t\t\t\tconst { coachName, idCode, id_card1, id_card2 } = this.tempForm;\n\n\t\t\t\tif (!coachName || !idCode || id_card1.length === 0 || id_card2.length === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请填写所有必填项并上传照片'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tlet p = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\n\t\t\t\tif (!p.test(idCode)) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请填写正确的身份证号',\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tlet shifuid = JSON.parse(uni.getStorageSync('shiInfo'))\n\t\t\t\tlet userId = (uni.getStorageSync('userId'))\n\t\t\t\tconsole.log(shifuid)\n\t\t\t\tconsole.log(userId)\n\t\t\t\t// Construct the payload for saving name and ID card information\n\t\t\t\tconst payload = {\n\t\t\t\t\tcoachName: coachName,\n\t\t\t\t\tidCode: idCode,\n\t\t\t\t\tid: shifuid.id,\n\t\t\t\t\tuserId: userId,\n\t\t\t\t\tidCard: [id_card1[0].path, id_card2[0].path], // Assuming imgsize is 1, so only one image per type\n\t\t\t\t};\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.updataInfoSF(payload); // Replace with your actual API call\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code === \"200\") { // Assuming \"0\" means success\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\ttitle: '身份信息保存成功',\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.showNameIdModal = false;\n\t\t\t\t\t\t// You might want to re-attempt the quote submission or refresh data here\n\t\t\t\t\t\t// For now, let's just close the modal.\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg || '身份信息保存失败'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'error',\n\t\t\t\t\t\ttitle: error.message || '身份信息保存失败'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// Jump to master settlement page\n\t\t\tgoToSettle() {\n\t\t\t\tthis.masterModalShow = false;\n\t\t\t\tuni.redirectTo({\n\t\t\t\t\turl: '/shifu/Settle'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// Format mobile number with ***** in the middle, unless payType.payType is 7, 3, or 5\n\t\t\tformatMobile(mobile) {\n\t\t\t\tif (!mobile) return '';\n\t\t\t\tif (this.payType.payType) return mobile;\n\n\t\t\t\treturn mobile;\n\t\t\t},\n\t\t\t// Format address to hide last 7 characters, unless payType.payType is 7, 3, or 5\n\t\t\tformatAddress(address) {\n\t\t\t\tif (!address) return '';\n\t\t\t\tif (this.payType.payType) return address;\n\n\t\t\t\treturn address;\n\t\t\t},\n\t\t\t// Format house number with ***** in the middle, unless payType.payType is 7, 3, or 5\n\t\t\tformatHouseNumber(houseNumber) {\n\t\t\t\tif (!houseNumber) return '';\n\t\t\t\tif (this.payType.payType === 7) return houseNumber;\n\n\t\t\t\treturn houseNumber;\n\t\t\t},\n\t\t\t// Format orderCode (keeping original format but removing '无')\n\t\t\tformatOrderCode(orderCode) {\n\t\t\t\treturn orderCode || '';\n\t\t\t},\n\t\t\t// Validate if the URL(s) contain valid image URLs\n\t\t\tisValidImageUrl(val) {\n\t\t\t\tif (!val || typeof val !== 'string') return false;\n\t\t\t\tconst urls = val.split(',').map(url => url.trim());\n\t\t\t\tconst imageRegex = /^(https?:\\/\\/).*\\.(png|jpg|jpeg|gif|bmp|webp)$/i;\n\t\t\t\treturn urls.some(url => imageRegex.test(url));\n\t\t\t},\n\t\t\t// Split comma-separated image URLs\n\t\t\tsplitImageUrls(val) {\n\t\t\t\tif (!val || typeof val !== 'string') return [];\n\t\t\t\treturn val.split(',').map(url => url.trim()).filter(url => url !== '');\n\t\t\t},\n\t\t\t// Handle navigation\n\t\t\tgodh() {\n\t\t\t\tuni.openLocation({\n\t\t\t\t\tlatitude: Number(this.selectedItem.lat) || 0,\n\t\t\t\t\tlongitude: Number(this.selectedItem.lng) || 0,\n\t\t\t\t\tscale: 18,\n\t\t\t\t\tname: this.selectedItem.address || '未知地址',\n\t\t\t\t\taddress: this.selectedItem.addressInfo || '未知地址信息',\n\t\t\t\t\tsuccess: () => console.log('Navigation opened'),\n\t\t\t\t\tfail: err => console.error('Navigation error:', err)\n\t\t\t\t});\n\t\t\t},\n\t\t\t// Fetch order details\n\t\t\tasync getDetail() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.orderdetM(this.id);\n\t\t\t\t\tconsole.log('API Response:', res);\n\t\t\t\t\tconsole.log('coachStatus:', res.data.coachStatus);\n\t\t\t\t\tif (res.data.coachStatus === 1) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '师傅状态在审核中',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tif (res.data.coachStatus === -1 || res.data.coachStatus === 4) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '你还不是师傅',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tthis.Info = res.data || {};\n\t\t\t\t\tthis.selectedItem = {\n\t\t\t\t\t\torderCode: res.data.orderCode || '',\n\t\t\t\t\t\tgoodsName: res.data.goodsName || '',\n\t\t\t\t\t\tcreateTime: res.data.createTime || 0,\n\t\t\t\t\t\tmobile: res.data.mobile || '',\n\t\t\t\t\t\taddressInfo: res.data.addressInfo || '',\n\t\t\t\t\t\taddress: res.data.address || '',\n\t\t\t\t\t\thouseNumber: res.data.houseNumber || '',\n\t\t\t\t\t\tlat: res.data.lat || 0,\n\t\t\t\t\t\tlng: res.data.lng || 0,\n\t\t\t\t\t\ttype: res.data.type || 0, // Add order type\n\t\t\t\t\t\tid: res.data.id || this.id // Add order ID\n\t\t\t\t\t};\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error('API Error:', err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取订单详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// Handle image load errors\n\t\t\tonImageError(url, index, imgIndex) {\n\t\t\t\tconsole.error(`Failed to load image: ${url}`);\n\t\t\t\tthis.imageErrors.push(`${index}-${imgIndex}`);\n\t\t\t\tconst urls = this.splitImageUrls(this.Info.settingOrderList[index].val);\n\t\t\t\turls[imgIndex] = this.fallbackImage;\n\t\t\t\tthis.$set(this.Info.settingOrderList[index], 'val', urls.join(','));\n\t\t\t},\n\t\t\t// Show image using uni.previewImage\n\t\t\tpreviewImage(url) {\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: this.splitImageUrls(this.Info.settingOrderList.find(item => item.val.includes(url)).val),\n\t\t\t\t\tcurrent: url,\n\t\t\t\t});\n\t\t\t},\n\t\t\t// Close image modal\n\t\t\tcloseImageModal() {\n\t\t\t\tthis.showImageModal = false;\n\t\t\t\tthis.currentImage = '';\n\t\t\t}\n\t\t},\n\t\tasync onLoad(options) {\n\t\t\t\tthis.$api.base.getConfig().then(res => {\n\t\t\t\t\t\t\t// console.log(res)\n\t\t\t\t\t\t\tthis.getconfigs = res\n\t\t\t\t\t\t\tconsole.log(this.getconfigs.masterNotice)\n\t\t\t\t\t\t})\n\t\t\t// this.configInfo = uni.getStorageSync('configInfo')\n\t\t\t// console.log(this.configInfo)\n\t\t\tthis.id = options.id || '';\n\t\t\tthis.payType = uni.getStorageSync('orderdetails') || {};\n\t\t\tconsole.log('payType:', this.payType);\n\t\t\tconsole.log('payType.payType:', this.payType.payType);\n\t\t\tconsole.log('Page options:', options);\n\t\t\tif (!this.id) {\n\t\t\t\tconsole.error('No order ID provided');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '订单ID缺失',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tawait this.getDetail();\n\t\t\tthis.ready = true;\n\t\t},\n\t\tonUnload() {\n\t\t\tuni.removeStorageSync('orderdetails');\n\t\t\tconsole.log('Removed orderdetails from local storage');\n\t\t}\n\t};\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(180deg, #f5f7fa 0%, #e4e7ed 100%);\n\t\tpadding: 32rpx 24rpx 120rpx 24rpx;\n\t\t/* 底部增加padding为报价按钮留空间 */\n\t\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n\t}\n\n\t.box {\n\t\tmargin: 0 auto;\n\t\tmax-width: 690rpx;\n\t\tbackground: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n\t\tpadding: 32rpx;\n\t}\n\n\t/* Title Styling */\n\t.title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 700;\n\t\tcolor: #1a1a1a;\n\t\tmargin-bottom: 24rpx;\n\n\t\tspan {\n\t\t\tcolor: #1a1a1a; /* For required fields */\n\t\t}\n\n\t\t.title-icon {\n\t\t\twidth: 48rpx;\n\t\t\theight: 48rpx;\n\t\t\tmargin-right: 16rpx;\n\t\t}\n\t}\n\n\t/* Info Box Styling */\n\t.info-box {\n\t\tbackground: #fafafa;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 24rpx;\n\t\tmargin-bottom: 32rpx;\n\t}\n\n\t.info-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 16rpx 0;\n\t\tborder-bottom: 1rpx solid #e5e7eb;\n\n\t\t&:last-child {\n\t\t\tborder-bottom: none;\n\t\t}\n\n\t\t.label {\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #6b7280;\n\t\t}\n\n\t\t.value {\n\t\t\tfont-size: 30rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #1a1a1a;\n\t\t\tmax-width: 420rpx;\n\t\t\twhite-space: normal;\n\t\t\tword-break: break-all;\n\t\t\tline-height: 1.4;\n\n\t\t\t&.align-right {\n\t\t\t\ttext-align: right;\n\t\t\t}\n\t\t}\n\t}\n\n\t.navigation-btn {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: 180rpx;\n\t\theight: 64rpx;\n\t\tbackground: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);\n\t\tborder-radius: 32rpx;\n\t\tcolor: #ffffff;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t\tmargin-top: 24rpx;\n\t\tmargin-left: auto;\n\n\t\t.nav-icon {\n\t\t\twidth: 32rpx;\n\t\t\theight: 32rpx;\n\t\t\tmargin-right: 12rpx;\n\t\t}\n\t}\n\n\t/* Dynamic Section Styling */\n\t.dynamic-section {\n\t\tmargin-bottom: 32rpx;\n\t}\n\n\t.img-box {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 16rpx;\n\t\tmargin-top: 24rpx;\n\t}\n\n\t.dynamic-image {\n\t\twidth: 196rpx;\n\t\theight: 196rpx;\n\t\tborder-radius: 16rpx;\n\t\tborder: 1rpx solid #e5e7eb;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n\t}\n\n\t/* Image Modal Styling */\n\t.image-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 1000;\n\t}\n\n\t.modal-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tmax-width: 90%;\n\t\tmax-height: 90%;\n\t}\n\n\t.modal-image {\n\t\tmax-width: 100%;\n\t\tmax-height: 80vh;\n\t\tborder-radius: 16rpx;\n\t}\n\n\t.close-btn {\n\t\tmargin-top: 20rpx;\n\t\tpadding: 16rpx 32rpx;\n\t\tbackground: #ffffff;\n\t\tborder-radius: 32rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #1a1a1a;\n\t\tfont-weight: 500;\n\t}\n\n\t/* Text and Notes Styling */\n\t.text-box {\n\t\tmargin-top: 24rpx;\n\t\tbackground: #f3f4f6;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 16rpx 24rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #4b5563;\n\t}\n\n\t.notes-box {\n\t\tmargin-top: 24rpx;\n\t\tbackground: #f9fafb;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 24rpx 32rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #374151;\n\t\tline-height: 1.5;\n\t\tword-break: break-all;\n\t}\n\n\t/* Bottom quote area style */\n\t.bottom-quote-section {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbackground: #ffffff;\n\t\tborder-top: 1rpx solid #e5e7eb;\n\t\tpadding: 24rpx 32rpx;\n\t\tpadding-bottom: calc(24rpx + env(safe-area-inset-bottom));\n\t\tz-index: 100;\n\t}\n\n\t.quote-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: linear-gradient(90deg, #2E80FE 0%, #5BA0FF 100%);\n\t\tborder-radius: 44rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #ffffff;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(46, 128, 254, 0.3);\n\t}\n\n\t/* Quote popup style */\n\t.quote-popup-box {\n\t\tpadding: 40rpx 30rpx;\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx 20rpx 0 0;\n\t\tmin-height: 400rpx;\n\n\t\t.popup-title {\n\t\t\ttext-align: center;\n\t\t\tfont-size: 36rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #1a1a1a;\n\t\t\tmargin-bottom: 40rpx;\n\t\t}\n\n\t\t.popup-subtitle {\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #374151;\n\t\t\tmargin-bottom: 20rpx;\n\t\t}\n\n\t\t.money-input {\n\t\t\tmargin-bottom: 40rpx;\n\t\t}\n\n\t\t.confirm-quote-btn {\n\t\t\twidth: 100%;\n\t\t\theight: 88rpx;\n\t\t\tbackground: linear-gradient(90deg, #2E80FE 0%, #5BA0FF 100%);\n\t\t\tborder-radius: 44rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #ffffff;\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(46, 128, 254, 0.3);\n\t\t}\n\t}\n\n\t.image-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\twidth: 100vw;\n\t\theight: 100vh;\n\t\tbackground: rgba(0, 0, 0, 0.8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 1000;\n\t}\n\n\t.modal-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\twidth: 100vw;\n\t\theight: 100vh;\n\t\tposition: relative;\n\t}\n\n\t.modal-image {\n\t\twidth: 100vw;\n\t\theight: 100vh;\n\t\tobject-fit: contain;\n\t\t/* Ensures image scales to fit without distortion */\n\t\tborder-radius: 0;\n\t\t/* Remove border-radius for full-screen effect */\n\t}\n\n\t.close-btn {\n\t\tposition: absolute;\n\t\tbottom: 20rpx;\n\t\tpadding: 16rpx 32rpx;\n\t\tbackground: #ffffff;\n\t\tborder-radius: 32rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #1a1a1a;\n\t\tfont-weight: 500;\n\t\tz-index: 1100;\n\t\t/* Ensure button is above image */\n\t}\n\n\t/* Optimized styles for the name/ID modal content */\n\t.slot-content {\n\t\tpadding: 20rpx 0;\n\t}\n\n\t.main_item {\n\t\tmargin-bottom: 32rpx;\n\t\tpadding: 0 24rpx;\n\n\t\t.title {\n\t\t\tmargin-bottom: 16rpx;\n\t\t\tfont-size: 30rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #1a1a1a;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\tspan {\n\t\t\t\tcolor: #1A1A1A;\n\t\t\t\tmargin-right: 8rpx;\n\t\t\t}\n\t\t}\n\n\t\t.modal-input {\n\t\t\twidth: 100%;\n\t\t\theight: 80rpx;\n\t\t\tbackground: #f8f8f8;\n\t\t\tborder: 1rpx solid #e5e7eb;\n\t\t\tborder-radius: 12rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 400;\n\t\t\tline-height: 80rpx;\n\t\t\tpadding: 0 24rpx;\n\t\t\tbox-sizing: border-box;\n\t\t\ttransition: all 0.2s ease-in-out;\n\n\t\t\t&:focus {\n\t\t\t\tborder-color: #2e80fe;\n\t\t\t\tbackground: #ffffff;\n\t\t\t\tbox-shadow: 0 0 8rpx rgba(46, 128, 254, 0.2);\n\t\t\t}\n\n\t\t\t&:disabled {\n\t\t\t\tbackground: #f0f0f0;\n\t\t\t\tcolor: #999;\n\t\t\t\tcursor: not-allowed;\n\t\t\t}\n\t\t}\n\n\t\t.card {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\tgap: 16rpx;\n\n\t\t\t.card_item {\n\t\t\t\twidth: 48%;\n\t\t\t\tbackground: #f2fafe;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n\t\t\t\ttransition: transform 0.2s ease-in-out;\n\n\t\t\t\t&:hover {\n\t\t\t\t\ttransform: translateY(-4rpx);\n\t\t\t\t}\n\n\t\t\t\t.top {\n\t\t\t\t\theight: 180rpx;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tpadding-top: 20rpx;\n\n\t\t\t\t\t.das {\n\t\t\t\t\t\tmargin: 0 auto;\n\t\t\t\t\t\twidth: 85%;\n\t\t\t\t\t\theight: 120rpx;\n\t\t\t\t\t\tborder: 2rpx dashed #2e80fe;\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\tpadding: 10rpx;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\n\t\t\t\t\t\t.up {\n\t\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.bottom {\n\t\t\t\t\theight: 60rpx;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tbackground: linear-gradient(90deg, #2e80fe 0%, #5ba0ff 100%);\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tline-height: 60rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_order_details.vue?vue&type=style&index=0&id=53648d4e&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_order_details.vue?vue&type=style&index=0&id=53648d4e&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754814569465\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}