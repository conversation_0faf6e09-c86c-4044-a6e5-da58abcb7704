@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-56491a8a {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding: 20rpx 0;
}
.page .header.data-v-56491a8a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  font-weight: 500;
  color: #3b3b3b;
  padding: 0 30rpx;
  width: 750rpx;
  height: 118rpx;
  background: #ffffff;
}
.page .header .right.data-v-56491a8a {
  display: flex;
  align-items: center;
  color: #2e80fe;
}
.page .header .right .arrow.data-v-56491a8a {
  margin-left: 10rpx;
  font-size: 24rpx;
}
.page .alipay-info.data-v-56491a8a {
  margin-top: 20rpx;
  background: #ffffff;
  padding: 30rpx;
}
.page .alipay-info .info-item.data-v-56491a8a {
  margin-bottom: 30rpx;
}
.page .alipay-info .info-item.data-v-56491a8a:last-child {
  margin-bottom: 0;
}
.page .alipay-info .info-item .label.data-v-56491a8a {
  font-size: 28rpx;
  font-weight: 500;
  color: #3b3b3b;
  margin-bottom: 20rpx;
}
.page .mid.data-v-56491a8a {
  margin-top: 20rpx;
  width: 750rpx;
  height: 276rpx;
  background: #ffffff;
  padding: 0 30rpx;
  padding-top: 40rpx;
}
.page .mid .title.data-v-56491a8a {
  font-size: 28rpx;
  font-weight: 500;
  color: #3b3b3b;
}
.page .mid .top.data-v-56491a8a {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding-top: 28rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f2f3f6;
}
.page .mid .top .r_left.data-v-56491a8a {
  font-size: 28rpx;
  font-weight: 500;
  color: #e51837;
}
.page .mid .bottom.data-v-56491a8a {
  padding-top: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
}
.page .btn.data-v-56491a8a {
  margin: 60rpx auto 0;
  width: 690rpx;
  height: 98rpx;
  background: #2e80fe;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 98rpx;
  text-align: center;
}
.page .btn.data-v-56491a8a:disabled {
  background: #cccccc;
}
.page .tips.data-v-56491a8a {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
  text-align: center;
  margin-top: 20rpx;
}
.page .contact.data-v-56491a8a {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
  text-align: center;
  margin-top: 20rpx;
}
.page .contact .phone.data-v-56491a8a {
  color: #2e80fe;
  text-decoration: underline;
}
.slot-content.data-v-56491a8a {
  padding: 20rpx 0;
}
.main_item.data-v-56491a8a {
  margin-bottom: 32rpx;
  padding: 0 24rpx;
}
.main_item .title.data-v-56491a8a {
  margin-bottom: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #1a1a1a;
  display: flex;
  align-items: center;
}
.main_item .title ._span.data-v-56491a8a {
  color: #e72427;
  margin-right: 8rpx;
}
.main_item .modal-input.data-v-56491a8a {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border: 1rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 80rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  transition: all 0.2s ease-in-out;
}
.main_item .modal-input.data-v-56491a8a:focus {
  border-color: #2e80fe;
  background: #ffffff;
  box-shadow: 0 0 8rpx rgba(46, 128, 254, 0.2);
}
.main_item .modal-input.data-v-56491a8a:disabled {
  background: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}
.main_item .card.data-v-56491a8a {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}
.main_item .card .card_item.data-v-56491a8a {
  width: 48%;
  background: #f2fafe;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
}
.main_item .card .card_item.data-v-56491a8a:hover {
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
}
.main_item .card .card_item .top.data-v-56491a8a {
  height: 180rpx;
  width: 100%;
  padding-top: 20rpx;
}
.main_item .card .card_item .top .das.data-v-56491a8a {
  margin: 0 auto;
  width: 85%;
  height: 120rpx;
  border: 2rpx dashed #2e80fe;
  border-radius: 8rpx;
  padding: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.main_item .card .card_item .top .das .up.data-v-56491a8a {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.main_item .card .card_item .bottom.data-v-56491a8a {
  height: 60rpx;
  width: 100%;
  background: linear-gradient(90deg, #2e80fe 0%, #5ba0ff 100%);
  font-size: 24rpx;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  line-height: 60rpx;
}
.cash-type-modal.data-v-56491a8a {
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 30rpx;
}
.cash-type-modal .modal-header.data-v-56491a8a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.cash-type-modal .modal-header .modal-title.data-v-56491a8a {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.cash-type-modal .modal-header .modal-close.data-v-56491a8a {
  font-size: 40rpx;
  color: #999999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cash-type-modal .cash-type-list .cash-type-item.data-v-56491a8a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.cash-type-modal .cash-type-list .cash-type-item.data-v-56491a8a:last-child {
  border-bottom: none;
}
.cash-type-modal .cash-type-list .cash-type-item.active .type-name.data-v-56491a8a {
  color: #2e80fe;
}
.cash-type-modal .cash-type-list .cash-type-item .type-name.data-v-56491a8a {
  font-size: 30rpx;
  color: #333333;
}
.cash-type-modal .cash-type-list .cash-type-item .type-check.data-v-56491a8a {
  font-size: 32rpx;
  color: #2e80fe;
  font-weight: bold;
}

