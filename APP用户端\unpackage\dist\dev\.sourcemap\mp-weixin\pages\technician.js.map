{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/technician.vue?4dcd", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/technician.vue?23e6", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/technician.vue?62fb", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/technician.vue?d1a6", "uni-app:///pages/technician.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/technician.vue?8ce1", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/technician.vue?6720"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "keyword", "categories", "selectedCategoryId", "selectedSubCategoryId", "activeSubCategoryId", "scrollToId", "loading", "error", "tmplIds", "expandSubCategories", "initialCategoryId", "headerHeight", "isScrolling", "computed", "currentServices", "methods", "selectCategory", "id", "console", "selectSubCategory", "onScroll", "setTimeout", "dingyue", "templateId", "templateCategoryId", "uni", "success", "selectedTmplIds", "fail", "checkVisibleService", "query", "resolve", "headerInfo", "servicePositions", "activeServiceId", "threshold", "i", "pos", "closestIndex", "minDistance", "distance", "goToDetails", "url", "title", "icon", "goUrl", "getList", "$api", "response", "Array", "cat", "children", "sub", "setInitialCategory", "onLoad", "city", "position"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACqC;;;AAG9F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACyE72B;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC,UACA,IACA,IACA,IACA,8CACA;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MAAA;MACA;MAEA,4CACA;QAAA;MAAA,EACA;MACA;MAEA;MAEA;QACA,wCACA;UAAA;QAAA,EACA;MACA;MAEA,yDACA;QAAA;MAAA,EACA;MACA,gEACA,wBACA;IACA;EACA;EACAC;IACAC;MAEAC;MACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEAC,4FACAT;IACA;IACAU;MACA;MACA;MACA;QAAA;MAAA;MACA;QACA;MACA;MACAD;IACA;IACA;IACAE;MAAA;MACA;MAEA;MACA;MACAC;QACA;QACA;MACA;IACA;IACAC;MAAA;MACAJ;MACA;MACA;QACAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;MACAA;MACA;QAAA;UACAK;UACAC;QACA;MAAA;MACAC;QACAjB;QACAkB;UACAR;UACA;UACA;UACAS;YACAT;YACA;cACA;cACA;gBACA;kBACA;gBACA;cACA;gBACA;cACA;cACAA;YACA;UACA;UACAA;QACA;QACAU;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA;gBAAA,OACA;kBACAA;oBACAC;kBACA;gBACA;cAAA;gBAJAC;gBAKA;cAAA;gBAGA;gBACAF;gBAAA;gBAAA,OACA;kBACA;oBACAA;kBACA;kBACAA;oBACAC;kBACA;gBACA;cAAA;gBAPAE;gBASA;gBACAC;gBACAC;gBAEAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAH;gBAAA;cAAA;gBAHAE;gBAAA;gBAAA;cAAA;gBAQA;gBACA;kBACAE;kBACAC;kBAEA;oBACAF;oBACA;sBACAG;sBACA;wBACAD;wBACAD;sBACA;oBACA;kBACA;kBACAJ;gBACA;;gBAEA;gBACA;kBACA;kBACAhB;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAuB;MACAhB;QACAiB;QACAd;UACAV;UACAO;YACAkB;YACAC;UACA;QACA;MACA;IACA;IACAC;MACApB;QACAiB;MACA;IACA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACArB;kBACAkB;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;gBAAA,OAEAG;cAAA;gBAAAC;gBACA9B;gBACAjB;gBAAA,KACAgD;kBAAA;kBAAA;gBAAA;gBACAhD;gBAAA;gBAAA;cAAA;gBAAA,MACA+C;kBAAA;kBAAA;gBAAA;gBACA/C;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAGA,kCACAA;kBAAA,uCACAiD;oBACAjC;oBACAkC;sBAAA,uCACAC;wBACAnC;sBAAA;oBAAA,CACA;kBAAA;gBAAA,CACA,EACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAC;gBACAO;kBACAkB;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAS;MAAA;MACA;QACAnC;QACA;QACA;UAAA;QAAA;QAEA;UACA;UACA;QACA;UACA;UACA;QACA;;QAEA;QACA;UACA;YACA;UACA;QACA;QAEAA,6FACAT;MACA;QACA;QACAgB;UACAkB;UACAC;QACA;MACA;IACA;EACA;EACAU;IACApC;IACA;IAEA;IACA;MACAqC;QACAC;MACA;IACA;IACAtC;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClYA;AAAA;AAAA;AAAA;AAAquC,CAAgB,wtCAAG,EAAC,C;;;;;;;;;;;ACAzvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/technician.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/technician.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./technician.vue?vue&type=template&id=5d289ec6&scoped=true&\"\nvar renderjs\nimport script from \"./technician.vue?vue&type=script&lang=js&\"\nexport * from \"./technician.vue?vue&type=script&lang=js&\"\nimport style0 from \"./technician.vue?vue&type=style&index=0&id=5d289ec6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5d289ec6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/technician.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./technician.vue?vue&type=template&id=5d289ec6&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-search/u-search\" */ \"uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading && !_vm.error ? _vm.categories.length : null\n  var l0 =\n    !_vm.loading && !_vm.error && !!g0\n      ? _vm.__map(_vm.categories, function (category, __i0__) {\n          var $orig = _vm.__get_orig(category)\n          var g1 =\n            _vm.selectedCategoryId === category.id &&\n            category.children &&\n            category.children.length &&\n            _vm.expandSubCategories\n          return {\n            $orig: $orig,\n            g1: g1,\n          }\n        })\n      : null\n  var g2 = !_vm.loading && !_vm.error ? _vm.currentServices.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./technician.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./technician.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<tabbar :cur=\"1\"></tabbar>\n\t\t<view class=\"header\" id=\"header\">\n\t\t\t<u-search placeholder=\"空调维修\" v-model=\"keyword\" :showAction=\"false\" inputAlign=\"center\"\n\t\t\t\t@focus=\"goUrl('/user/search')\"></u-search>\n\t\t</view>\n\t\t<view class=\"main\">\n\t\t\t<view class=\"left\">\n\t\t\t\t<scroll-view scroll-y=\"true\" class=\"scrollL\">\n\t\t\t\t\t<view v-if=\"loading\" class=\"loading\">\n\t\t\t\t\t\t<text>加载中...</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else-if=\"error\" class=\"error\">\n\t\t\t\t\t\t<text>{{ error }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else-if=\"!categories.length\" class=\"no-content\">\n\t\t\t\t\t\t<text>暂无分类数据</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else class=\"left_item\" v-for=\"category in categories\" :key=\"category.id\"\n\t\t\t\t\t\t@tap=\"selectCategory(category.id)\">\n\t\t\t\t\t\t<view class=\"category_name\" :class=\"{ active: selectedCategoryId === category.id }\">\n\t\t\t\t\t\t\t{{ category.name }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tv-if=\"selectedCategoryId === category.id && category.children && category.children.length && expandSubCategories\"\n\t\t\t\t\t\t\tclass=\"sub_categories\">\n\t\t\t\t\t\t\t<view class=\"sub_category\" v-for=\"subCategory in category.children\" :key=\"subCategory.id\"\n\t\t\t\t\t\t\t\**********=\"selectSubCategory(subCategory.id)\"\n\t\t\t\t\t\t\t\t:class=\"{ active: activeSubCategoryId === subCategory.id }\">\n\t\t\t\t\t\t\t\t{{ subCategory.name }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<!-- 添加底部占位空间 -->\n\t\t\t\t\t<view class=\"bottom_placeholder\"></view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\n\t\t\t<view class=\"right\">\n\t\t\t\t<scroll-view scroll-y=\"true\" class=\"scrollR\" :scroll-into-view=\"scrollToId\" @scroll=\"onScroll\"\n\t\t\t\t\t:throttle=\"false\">\n\t\t\t\t\t<view v-if=\"loading\" class=\"loading\">\n\t\t\t\t\t\t<text>加载中...</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else-if=\"error\" class=\"error\">\n\t\t\t\t\t\t<text>{{ error }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else-if=\"currentServices.length\">\n\t\t\t\t\t\t<view class=\"right_box\" v-for=\"service in currentServices\" :key=\"service.id\"\n\t\t\t\t\t\t\t:id=\"'service-' + service.id\">\n\t\t\t\t\t\t\t<view class=\"title\">{{ service.name }}</view>\n\t\t\t\t\t\t\t<view class=\"img\">\n\t\t\t\t\t\t\t\t<view class=\"img_item\" v-for=\"item in service.serviceList\" :key=\"item.id\"\n\t\t\t\t\t\t\t\t\t@click=\"goToDetails(item.id)\">\n\t\t\t\t\t\t\t\t\t<image :src=\"item.cover || 'https://via.placeholder.com/144'\" mode=\"aspectFill\">\n\t\t\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t\t\t<view class=\"lname\">{{ item.title }}</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else class=\"no-content\">\n\t\t\t\t\t\t<text>暂无服务</text>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport tabbar from \"@/components/tabbar.vue\";\n\timport $api from \"@/api/index.js\";\n\n\texport default {\n\t\tcomponents: {\n\t\t\ttabbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tkeyword: \"\",\n\t\t\t\tcategories: [],\n\t\t\t\tselectedCategoryId: null,\n\t\t\t\tselectedSubCategoryId: null,\n\t\t\t\tactiveSubCategoryId: null, // 新增：当前高亮的子类ID\n\t\t\t\tscrollToId: \"\",\n\t\t\t\tloading: false,\n\t\t\t\terror: null,\n\t\t\t\ttmplIds: [\n\t\t\t\t\t'',\n\t\t\t\t\t'',\n\t\t\t\t\t'',\n\t\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t\t],\n\t\t\t\texpandSubCategories: false,\n\t\t\t\tinitialCategoryId: null,\n\t\t\t\theaderHeight: 0, // header高度\n\t\t\t\tisScrolling: false, // 是否正在滚动\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\tcurrentServices() {\n\t\t\t\tif (!this.selectedCategoryId) return [];\n\n\t\t\t\tconst selectedCategory = this.categories.find(\n\t\t\t\t\t(cat) => String(cat.id) === String(this.selectedCategoryId)\n\t\t\t\t);\n\t\t\t\tif (!selectedCategory) return [];\n\n\t\t\t\tif (!selectedCategory.children || !selectedCategory.children.length) return [];\n\n\t\t\t\tif (!this.selectedSubCategoryId) {\n\t\t\t\t\treturn selectedCategory.children.filter(\n\t\t\t\t\t\t(sub) => sub.serviceList && sub.serviceList.length > 0\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tconst selectedSubCategory = selectedCategory.children.find(\n\t\t\t\t\t(sub) => String(sub.id) === String(this.selectedSubCategoryId)\n\t\t\t\t);\n\t\t\t\treturn selectedSubCategory && selectedSubCategory.serviceList ?\n\t\t\t\t\t[selectedSubCategory] :\n\t\t\t\t\t[];\n\t\t\t},\n\t\t},\n\t\tmethods: {\n\t\t\tselectCategory(id) {\n\n\t\t\t\tid = String(id);\n\t\t\t\tif (this.selectedCategoryId === id) {\n\t\t\t\t\tthis.expandSubCategories = !this.expandSubCategories;\n\t\t\t\t\tthis.selectedSubCategoryId = null;\n\t\t\t\t\tthis.activeSubCategoryId = null;\n\t\t\t\t\tthis.scrollToId = \"\";\n\t\t\t\t} else {\n\t\t\t\t\tthis.selectedCategoryId = id;\n\t\t\t\t\tthis.selectedSubCategoryId = null;\n\t\t\t\t\tthis.activeSubCategoryId = null;\n\t\t\t\t\tthis.scrollToId = \"\";\n\t\t\t\t\tthis.expandSubCategories = true;\n\t\t\t\t}\n\n\t\t\t\tconsole.log(\"Selected Category ID:\", this.selectedCategoryId, \"Expand Subcategories:\", this\n\t\t\t\t\t.expandSubCategories);\n\t\t\t},\n\t\t\tselectSubCategory(id) {\n\t\t\t\tthis.selectedSubCategoryId = String(id);\n\t\t\t\tthis.activeSubCategoryId = String(id);\n\t\t\t\tconst service = this.currentServices.find((s) => String(s.id) === String(id));\n\t\t\t\tif (service) {\n\t\t\t\t\tthis.scrollToId = `service-${service.id}`;\n\t\t\t\t}\n\t\t\t\tconsole.log(\"Selected SubCategory ID:\", this.selectedSubCategoryId, \"Scroll To:\", this.scrollToId);\n\t\t\t},\n\t\t\t// 滚动事件处理\n\t\t\tonScroll(e) {\n\t\t\t\tif (this.isScrolling) return;\n\n\t\t\t\tthis.isScrolling = true;\n\t\t\t\t// 使用节流，避免频繁触发\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.checkVisibleService(e.detail.scrollTop);\n\t\t\t\t\tthis.isScrolling = false;\n\t\t\t\t}, 50);\n\t\t\t},\n\t\t\tdingyue() {\n\t\t\t\tconsole.log('dingyue called');\n\t\t\t\tconst allTmplIds = this.tmplIds;\n\t\t\t\tif (allTmplIds.length < 3) {\n\t\t\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t// \ticon: 'none',\n\t\t\t\t\t// \ttitle: '模板ID不足'\n\t\t\t\t\t// });\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());\n\t\t\t\tconst selectedTmplIds = shuffled.slice(0, 3);\n\t\t\t\tconsole.log(\"Selected template IDs:\", selectedTmplIds);\n\t\t\t\tconst templateData = selectedTmplIds.map((id, index) => ({\n\t\t\t\t\ttemplateId: id,\n\t\t\t\t\ttemplateCategoryId: index === 0 ? 10 : 5\n\t\t\t\t}));\n\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\ttmplIds: selectedTmplIds,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('requestSubscribeMessage result:', res);\n\t\t\t\t\t\tthis.templateCategoryIds = [];\n\t\t\t\t\t\tlet count = 0;\n\t\t\t\t\t\tselectedTmplIds.forEach((templId, index) => {\n\t\t\t\t\t\t\tconsole.log(`Template ${templId} status: ${res[templId]}`);\n\t\t\t\t\t\t\tif (res[templId] === 'accept') {\n\t\t\t\t\t\t\t\tconst templateCategoryId = templateData[index].templateCategoryId;\n\t\t\t\t\t\t\t\tif (templateCategoryId === 10) {\n\t\t\t\t\t\t\t\t\tfor (let i = 0; i < 15; i++) {\n\t\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tconsole.log('Accepted message push for template:', templId);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.log('Updated templateCategoryIds:', this.templateCategoryIds);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 检查当前可见的服务区域\n\t\t\tasync checkVisibleService(scrollTop) {\n\t\t\t\tif (!this.currentServices.length) return;\n\n\t\t\t\ttry {\n\t\t\t\t\t// 获取header高度作为参考线\n\t\t\t\t\tif (!this.headerHeight) {\n\t\t\t\t\t\tconst query = uni.createSelectorQuery().in(this);\n\t\t\t\t\t\tconst headerInfo = await new Promise((resolve) => {\n\t\t\t\t\t\t\tquery.select('#header').boundingClientRect((data) => {\n\t\t\t\t\t\t\t\tresolve(data);\n\t\t\t\t\t\t\t}).exec();\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.headerHeight = headerInfo ? headerInfo.height : 80; // 默认80px\n\t\t\t\t\t}\n\n\t\t\t\t\t// 获取所有服务区域的位置信息\n\t\t\t\t\tconst query = uni.createSelectorQuery().in(this);\n\t\t\t\t\tconst servicePositions = await new Promise((resolve) => {\n\t\t\t\t\t\tthis.currentServices.forEach((service, index) => {\n\t\t\t\t\t\t\tquery.select(`#service-${service.id}`).boundingClientRect();\n\t\t\t\t\t\t});\n\t\t\t\t\t\tquery.exec((res) => {\n\t\t\t\t\t\t\tresolve(res);\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\n\t\t\t\t\t// 找到当前在header底部位置的服务区域\n\t\t\t\t\tlet activeServiceId = null;\n\t\t\t\t\tconst threshold = this.headerHeight + 20; // header底部 + 一些偏移\n\n\t\t\t\t\tfor (let i = 0; i < servicePositions.length; i++) {\n\t\t\t\t\t\tconst pos = servicePositions[i];\n\t\t\t\t\t\tif (pos && pos.top <= threshold && pos.bottom > threshold) {\n\t\t\t\t\t\t\tactiveServiceId = this.currentServices[i].id;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// 如果没有找到完全符合条件的，找最接近的\n\t\t\t\t\tif (!activeServiceId && servicePositions.length > 0) {\n\t\t\t\t\t\tlet closestIndex = 0;\n\t\t\t\t\t\tlet minDistance = Math.abs(servicePositions[0].top - threshold);\n\n\t\t\t\t\t\tfor (let i = 1; i < servicePositions.length; i++) {\n\t\t\t\t\t\t\tconst pos = servicePositions[i];\n\t\t\t\t\t\t\tif (pos) {\n\t\t\t\t\t\t\t\tconst distance = Math.abs(pos.top - threshold);\n\t\t\t\t\t\t\t\tif (distance < minDistance && pos.top <= threshold + 100) {\n\t\t\t\t\t\t\t\t\tminDistance = distance;\n\t\t\t\t\t\t\t\t\tclosestIndex = i;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tactiveServiceId = this.currentServices[closestIndex].id;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 更新高亮的子类\n\t\t\t\t\tif (activeServiceId && String(activeServiceId) !== String(this.activeSubCategoryId)) {\n\t\t\t\t\t\tthis.activeSubCategoryId = String(activeServiceId);\n\t\t\t\t\t\tconsole.log(\"Active SubCategory ID changed to:\", this.activeSubCategoryId);\n\t\t\t\t\t}\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error(\"Error in checkVisibleService:\", err);\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoToDetails(id) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `../user/commodity_details?id=${id}`,\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error(\"Navigation failed:\", err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: \"跳转失败: \" + err.errMsg,\n\t\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoUrl(url) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync getList(city) {\n\t\t\t\tif (!city || !city.position) {\n\t\t\t\t\tthis.error = \"未获取到城市信息\";\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: \"未获取到城市信息\",\n\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.loading = true;\n\t\t\t\tthis.error = null;\n\t\t\t\ttry {\n\t\t\t\t\tconst response = await $api.service.setserviceCate(city.position);\n\t\t\t\t\tconsole.log(response)\n\t\t\t\t\tlet categories = [];\n\t\t\t\t\tif (Array.isArray(response)) {\n\t\t\t\t\t\tcategories = response;\n\t\t\t\t\t} else if (response.data && Array.isArray(response.data)) {\n\t\t\t\t\t\tcategories = response.data;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(\"Invalid or empty data from API\");\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.categories = Object.freeze(\n\t\t\t\t\t\tcategories.map(cat => ({\n\t\t\t\t\t\t\t...cat,\n\t\t\t\t\t\t\tid: String(cat.id),\n\t\t\t\t\t\t\tchildren: cat.children ? cat.children.map(sub => ({\n\t\t\t\t\t\t\t\t...sub,\n\t\t\t\t\t\t\t\tid: String(sub.id)\n\t\t\t\t\t\t\t})) : []\n\t\t\t\t\t\t}))\n\t\t\t\t\t);\n\n\t\t\t\t\tthis.setInitialCategory();\n\t\t\t\t} catch (err) {\n\t\t\t\t\tthis.error = \"数据加载失败: \" + err.message;\n\t\t\t\t\tconsole.error(\"Error in getList:\", err);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: this.error,\n\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\tsetInitialCategory() {\n\t\t\t\tif (this.categories.length > 0) {\n\t\t\t\t\tconsole.log(\"Initial Category ID from onLoad:\", this.initialCategoryId);\n\t\t\t\t\tconst targetId = this.initialCategoryId ? String(this.initialCategoryId) : null;\n\t\t\t\t\tconst categoryExists = targetId && this.categories.some(cat => String(cat.id) === targetId);\n\n\t\t\t\t\tif (categoryExists) {\n\t\t\t\t\t\tthis.selectedCategoryId = targetId;\n\t\t\t\t\t\tthis.expandSubCategories = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.selectedCategoryId = String(this.categories[0].id);\n\t\t\t\t\t\tthis.expandSubCategories = false;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 初始化第一个子类为高亮\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tif (this.currentServices.length > 0) {\n\t\t\t\t\t\t\tthis.activeSubCategoryId = String(this.currentServices[0].id);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tconsole.log(\"Set selectedCategoryId:\", this.selectedCategoryId, \"expandSubCategories:\", this\n\t\t\t\t\t\t.expandSubCategories);\n\t\t\t\t} else {\n\t\t\t\t\tthis.error = \"分类数据为空\";\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: \"分类数据为空\",\n\t\t\t\t\t\ticon: \"none\",\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tonLoad(e) {\n\t\t\tconsole.log(\"onLoad params:\", JSON.stringify(e, null, 2));\n\t\t\tthis.initialCategoryId = e.id ? String(e.id) : null;\n\n\t\t\tlet city = uni.getStorageSync(\"city\");\n\t\t\tif (!city) {\n\t\t\t\tcity = {\n\t\t\t\t\tposition: \"阜阳市\"\n\t\t\t\t};\n\t\t\t}\n\t\t\tconsole.log(\"City:\", JSON.stringify(city, null, 2));\n\n\t\t\tthis.getList(city);\n\t\t},\n\t};\n</script>\n\n<style scoped>\n\t.page {\n\t\theight: 100vh;\n\t\toverflow: hidden;\n\t\tbackground-color: #f8f8f8;\n\t\t/* 确保 tabbar 不遮挡内容 */\n\t\tbox-sizing: border-box;\n\t\tpadding-bottom: 80rpx;\n\t}\n\n\t.header {\n\t\tpadding: 0 15rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground-color: #fff;\n\t\theight: 10vh;\n\t\tposition: relative;\n\t\tz-index: 10;\n\t}\n\n\t.main {\n\t\tdisplay: flex;\n\t\t/* 计算剩余高度 */\n\t\theight: calc(90vh - 80rpx);\n\t}\n\n\t.left {\n\t\twidth: 190rpx;\n\t\tbackground-color: #f8f8f8;\n\t\t/* 确保 left 容器高度和 main 一致 */\n\t\theight: 100%;\n\t}\n\n\t.scrollL {\n\t\t/* 修复：设置准确的滚动容器高度 */\n\t\theight: 100%;\n\t\toverflow-y: auto;\n\t\t/* 确保内容可以完整滚动 */\n\t\tpadding-bottom: 20rpx;\n\t}\n\n\t.left_item {\n\t\tpadding: 0;\n\t\tmin-height: 100rpx;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t\ttransition: all 0.2s;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.category_name {\n\t\theight: 100rpx;\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 0 20rpx;\n\t\tborder-left: 6rpx solid transparent;\n\t\ttransition: all 0.2s;\n\t\t/* 防止文本换行 */\n\t\twhite-space: nowrap;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.category_name.active {\n\t\tcolor: #fff;\n\t\tbackground-color: #2e80fe;\n\t\tborder-left-color: #2e80fe;\n\t}\n\n\t.sub_categories {\n\t\twidth: 100%;\n\t\tpadding-left: 35rpx;\n\t\tfont-size: 20rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tbackground-color: #fff;\n\t}\n\n\t.sub_category {\n\t\tmin-height: 80rpx;\n\t\tline-height: 80rpx;\n\t\tfont-size: 26rpx;\n\t\tfont-weight: 400;\n\t\tcolor: #666;\n\t\ttransition: color 0.2s;\n\t\tposition: relative;\n\t}\n\n\t.sub_category.active {\n\t\tcolor: #2e80fe;\n\t\tfont-weight: 500;\n\t}\n\n\t.sub_category.active::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\tleft: -15rpx;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\twidth: 6rpx;\n\t\theight: 30rpx;\n\t\tbackground-color: #2e80fe;\n\t\tborder-radius: 3rpx;\n\t}\n\n\t/* 新增：底部占位空间样式 */\n\t.bottom_placeholder {\n\t\theight: 40rpx;\n\t\twidth: 100%;\n\t}\n\n\t.right {\n\t\tflex: 1;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 12rpx 12rpx 0 0;\n\t\tmargin-left: 10rpx;\n\t\t/* 确保 right 容器高度和 main 一致 */\n\t\theight: 100%;\n\t\toverflow: hidden; /* 防止内部滚动条影响布局 */\n\t}\n\n\t.scrollR {\n\t\theight: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.right_box {\n\t\tpadding: 24rpx 9rpx;\n\t}\n\n\t.right_box .title {\n\t\tpadding-left: 15rpx;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.img {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: flex-start;\n\t}\n\n\t.img_item {\n\t\twidth: 220rpx;\n\t\tmargin: 10rpx 15rpx;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 12rpx;\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 20rpx;\n\t\ttransition: transform 0.2s;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.img_item:hover {\n\t\ttransform: translateY(-4rpx);\n\t}\n\n\t.img_item image {\n\t\twidth: 144rpx;\n\t\theight: 144rpx;\n\t\tborder-radius: 12rpx;\n\t\tmargin-bottom: 12rpx;\n\t}\n\n\t.lname {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 400;\n\t\tcolor: #333;\n\t\ttext-align: center;\n\t\toverflow: hidden;\n\t\twhite-space: nowrap;\n\t\ttext-overflow: ellipsis;\n\t\twidth: 100%;\n\t}\n\n\t.no-content,\n\t.loading,\n\t.error {\n\t\ttext-align: center;\n\t\tcolor: #999;\n\t\tfont-size: 28rpx;\n\t\tpadding: 40rpx;\n\t}\n\n\t.error {\n\t\tcolor: #ff4d4f;\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./technician.vue?vue&type=style&index=0&id=5d289ec6&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./technician.vue?vue&type=style&index=0&id=5d289ec6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754814577469\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}