@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-9870f036 {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding: 20rpx 0;
}
.page .header.data-v-9870f036 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  font-weight: 500;
  color: #3b3b3b;
  padding: 0 30rpx;
  width: 750rpx;
  height: 118rpx;
  background: #ffffff;
}
.page .header .right.data-v-9870f036 {
  display: flex;
  align-items: center;
  color: #2e80fe;
}
.page .header .right .arrow.data-v-9870f036 {
  margin-left: 10rpx;
  font-size: 24rpx;
}
.page .alipay-info.data-v-9870f036 {
  margin-top: 20rpx;
  background: #ffffff;
  padding: 30rpx;
}
.page .alipay-info .info-item.data-v-9870f036 {
  margin-bottom: 30rpx;
}
.page .alipay-info .info-item.data-v-9870f036:last-child {
  margin-bottom: 0;
}
.page .alipay-info .info-item .label.data-v-9870f036 {
  font-size: 28rpx;
  font-weight: 500;
  color: #3b3b3b;
  margin-bottom: 20rpx;
}
.page .mid.data-v-9870f036 {
  margin-top: 20rpx;
  width: 750rpx;
  height: 276rpx;
  background: #ffffff;
  padding: 0 30rpx;
  padding-top: 40rpx;
}
.page .mid .title.data-v-9870f036 {
  font-size: 28rpx;
  font-weight: 500;
  color: #3b3b3b;
}
.page .mid .top.data-v-9870f036 {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding-top: 28rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f2f3f6;
}
.page .mid .top .r_left.data-v-9870f036 {
  font-size: 28rpx;
  font-weight: 500;
  color: #e51837;
}
.page .mid .bottom.data-v-9870f036 {
  padding-top: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
}
.page .btn.data-v-9870f036 {
  margin: 60rpx auto 0;
  width: 690rpx;
  height: 98rpx;
  background: #2e80fe;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 98rpx;
  text-align: center;
}
.page .btn.data-v-9870f036:disabled {
  background: #cccccc;
}
.page .tips.data-v-9870f036 {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
  text-align: center;
  margin-top: 20rpx;
}
.page .contact.data-v-9870f036 {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
  text-align: center;
  margin-top: 20rpx;
}
.page .contact .phone.data-v-9870f036 {
  color: #2e80fe;
  text-decoration: underline;
}
.cash-type-modal.data-v-9870f036 {
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 30rpx;
}
.cash-type-modal .modal-header.data-v-9870f036 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.cash-type-modal .modal-header .modal-title.data-v-9870f036 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.cash-type-modal .modal-header .modal-close.data-v-9870f036 {
  font-size: 40rpx;
  color: #999999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cash-type-modal .cash-type-list .cash-type-item.data-v-9870f036 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.cash-type-modal .cash-type-list .cash-type-item.data-v-9870f036:last-child {
  border-bottom: none;
}
.cash-type-modal .cash-type-list .cash-type-item.active .type-name.data-v-9870f036 {
  color: #2e80fe;
}
.cash-type-modal .cash-type-list .cash-type-item .type-name.data-v-9870f036 {
  font-size: 30rpx;
  color: #333333;
}
.cash-type-modal .cash-type-list .cash-type-item .type-check.data-v-9870f036 {
  font-size: 32rpx;
  color: #2e80fe;
  font-weight: bold;
}

